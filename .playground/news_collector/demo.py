#!/usr/bin/env python3
"""
Demo script showing how to use the news collector
"""
import os
import sys
import json

# Add current directory to path for imports
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.append(current_dir)

from collector import NewsCollector
from collector_models import NewsArticle

def demo_news_collection():
    """Demonstrate news collection functionality"""
    print("News Collector Demo")
    print("=" * 50)
    
    # Initialize the collector
    collector = NewsCollector(os.path.join(current_dir, "config.yaml"))
    
    print(f"Configured sources:")
    print(f"- Web Search Sources: {len(collector.config.web_search_sources)}")
    print(f"- Specific Sources: {len(collector.config.specific_sources)}")
    print()
    
    # Collect news articles
    print("Collecting news articles...")
    articles = collector.collect_all_news()
    
    print(f"\nCollected {len(articles)} articles:")
    print("-" * 50)
    
    # Display results
    for i, article in enumerate(articles, 1):
        print(f"{i}. {article.title}")
        print(f"   Source: {article.source_name}")
        print(f"   URL: {article.url}")
        print(f"   Content: {article.content[:200]}..." if len(article.content) > 200 else f"   Content: {article.content}")
        print()
    
    # Export to JSON for further processing
    print("Exporting to JSON...")
    articles_data = []
    for article in articles:
        articles_data.append({
            "title": article.title,
            "url": article.url,
            "content": article.content,
            "source_name": article.source_name,
            "published_date": article.published_date.isoformat() if article.published_date else None
        })
    
    output_file = os.path.join(current_dir, "collected_news.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(articles_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Exported {len(articles)} articles to {output_file}")
    return articles

if __name__ == "__main__":
    articles = demo_news_collection()
