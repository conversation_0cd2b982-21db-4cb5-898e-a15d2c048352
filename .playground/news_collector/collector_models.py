from pydantic import BaseModel
from typing import List, Optional, Literal
from datetime import datetime


class WebSearchSource(BaseModel):
    """Configuration for web search-based news collection"""
    name: str
    query: str
    time_range: Literal["day", "week", "month", "year"] = "day"
    max_results: int = 10
    include_domains: Optional[List[str]] = None
    exclude_domains: Optional[List[str]] = None


class SpecificSource(BaseModel):
    """Configuration for specific URL-based news collection"""
    name: str
    url: str
    time_range: Literal["day", "week", "month", "year"] = "day"


class NewsCollectorConfig(BaseModel):
    """Main configuration for news collection"""
    web_search_sources: List[WebSearchSource] = []
    specific_sources: List[SpecificSource] = []


class NewsArticle(BaseModel):
    """Represents a collected news article"""
    title: str
    url: str
    content: str
    published_date: Optional[datetime] = None
    source_name: str
