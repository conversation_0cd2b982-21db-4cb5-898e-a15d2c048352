# News Collector

A simple and efficient news collection system that gathers carbon regulation and clean energy news from various sources.

## Features

- **Web Search Collection**: Search for news articles using <PERSON><PERSON>'s search API with configurable queries and time ranges
- **Specific Source Collection**: Extract articles from specific news website pages (like Reuters clean energy section)
- **Configurable Sources**: Easy YAML configuration for different news sources
- **AI-Powered URL Extraction**: Uses Pydantic AI to intelligently extract relevant article URLs from web pages
- **Structured Output**: Returns structured NewsArticle objects with title, URL, content, and source information

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables in `.env`:
```
TAVILY_API_KEY=your_tavily_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Configuration

Edit `config.yaml` to configure your news sources:

### Web Search Sources
Search for news articles using specific queries:
```yaml
web_search_sources:
  - name: "Clean Energy News Search"
    query: "clean energy regulations carbon policy sustainability"
    time_range: "day"  # day, week, month, year
    max_results: 5
    include_domains: []  # Optional: specific domains to include
    exclude_domains: []  # Optional: domains to exclude
```

### Specific Sources
Extract articles from specific news website pages:
```yaml
specific_sources:
  - name: "Reuters Clean Energy"
    url: "https://www.reuters.com/sustainability/clean-energy/"
    time_range: "day"
```

## Usage

### Basic Usage
```python
from collector import NewsCollector

# Initialize collector
collector = NewsCollector("config.yaml")

# Collect all news articles
articles = collector.collect_all_news()

# Process articles
for article in articles:
    print(f"Title: {article.title}")
    print(f"URL: {article.url}")
    print(f"Source: {article.source_name}")
    print(f"Content: {article.content[:200]}...")
    print()
```

### Running Scripts

1. **Test the collector**:
```bash
python test_collector.py
```

2. **Run demo collection**:
```bash
python demo.py
```

3. **Run basic collection**:
```bash
python collector.py
```

## Data Models

### NewsArticle
```python
class NewsArticle(BaseModel):
    title: str
    url: str
    content: str
    published_date: Optional[datetime] = None
    source_name: str
```

### Configuration Models
- `WebSearchSource`: Configuration for web search-based collection
- `SpecificSource`: Configuration for specific URL-based collection
- `NewsCollectorConfig`: Main configuration container

## How It Works

1. **Web Search Collection**:
   - Uses Tavily's search API to find relevant news articles
   - Filters by time range and other parameters
   - Extracts full content from each article URL

2. **Specific Source Collection**:
   - Extracts content from specific news website pages
   - Uses AI agent to identify article URLs from the page content
   - Extracts full content from each identified article

3. **Content Processing**:
   - Cleans and processes article content
   - Extracts titles from content when not available
   - Returns structured data for further processing

## Output

The collector returns a list of `NewsArticle` objects that can be:
- Processed directly in Python
- Exported to JSON for other systems
- Passed to further analysis pipelines

## Example Output

```json
[
  {
    "title": "Trump admin cancels $679 million for offshore wind projects",
    "url": "https://abcnews.go.com/Business/wireStory/trump-admin-cancels-679-million-offshore-wind-projects-125107388",
    "content": "WASHINGTON -- The Transportation Department on Friday canceled $679 million...",
    "source_name": "Clean Energy News Search",
    "published_date": null
  }
]
```

## Notes

- The specific source collection works best with well-structured news websites
- Time ranges are approximate and depend on the source's content freshness
- AI URL extraction provides intelligent filtering but may occasionally miss articles
- Error handling ensures the collector continues working even if individual sources fail
