#!/usr/bin/env python3
"""
Test script for the news collector
"""
import os
import sys
from dotenv import load_dotenv

# Add current directory to path for imports
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.append(current_dir)

from collector import NewsCollector
from collector_models import NewsArticle

def test_basic_functionality():
    """Test basic news collector functionality"""
    print("Testing News Collector...")
    
    try:
        # Initialize collector (it will load .env automatically)
        collector = NewsCollector(os.path.join(current_dir, "config.yaml"))
        print("✅ News collector initialized")
        
        # Check if API keys are available after initialization
        tavily_key = os.getenv("TAVILY_API_KEY")
        openrouter_key = os.getenv("OPENROUTER_API_KEY")
        
        if not tavily_key:
            print("❌ TAVILY_API_KEY not found")
            return False
        if not openrouter_key:
            print("❌ OPENROUTER_API_KEY not found")
            return False
        
        print("✅ API keys found")
        
        # Test web search collection (limit to 1 source for quick test)
        test_sources = collector.config.web_search_sources[:1]
        collector.config.web_search_sources = test_sources
        
        # Collect news
        articles = collector.collect_all_news()
        print(f"✅ Collected {len(articles)} articles")
        
        # Validate articles
        if articles:
            first_article = articles[0]
            assert isinstance(first_article, NewsArticle)
            assert first_article.title
            assert first_article.url
            assert first_article.content
            assert first_article.source_name
            print("✅ Articles have required fields")
            
            print("\nSample article:")
            print(f"Title: {first_article.title}")
            print(f"URL: {first_article.url}")
            print(f"Source: {first_article.source_name}")
            print(f"Content length: {len(first_article.content)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\nTesting Configuration...")
    
    try:
        collector = NewsCollector(os.path.join(current_dir, "config.yaml"))
        config = collector.config
        
        assert len(config.web_search_sources) > 0
        assert len(config.specific_sources) > 0
        
        # Test first web search source
        first_search = config.web_search_sources[0]
        assert first_search.name
        assert first_search.query
        assert first_search.time_range in ["day", "week", "month", "year"]
        
        # Test first specific source
        first_specific = config.specific_sources[0]
        assert first_specific.name
        assert first_specific.url
        assert first_specific.time_range in ["day", "week", "month", "year"]
        
        print("✅ Configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

if __name__ == "__main__":
    print("News Collector Test Suite")
    print("=" * 40)
    
    success = True
    success &= test_configuration()
    success &= test_basic_functionality()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    sys.exit(0 if success else 1)
