#!/usr/bin/env python3
"""
Simple test to verify AI pipeline integration works
"""
import sys
import os

# Navigate to AI parser directory and test import
ai_parser_dir = "/home/<USER>/dev/ai/carbon-regulation-news/.playground/ai_source_parser"
if ai_parser_dir not in sys.path:
    sys.path.insert(0, ai_parser_dir)

try:
    from news_pipeline_refactored import NewsExtractor, ExtractInput
    print("✅ AI pipeline import successful!")
    
    # Test with sample data
    sample_data = ExtractInput(
        url="https://example.com/test",
        outlet="Test Source",
        raw_content="Sample news article about carbon regulations and clean energy policy."
    )
    
    extractor = NewsExtractor()
    print("✅ NewsExtractor initialized successfully!")
    
    # This would normally extract the full NewsItem but might fail due to API limits
    # news_item = extractor.extract(sample_data)
    # print("✅ Extraction successful!")
    
except Exception as e:
    print(f"❌ AI pipeline test failed: {e}")
    import traceback
    traceback.print_exc()
