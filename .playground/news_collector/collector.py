import os
import yaml
from typing import List, Optional
from datetime import datetime, timed<PERSON><PERSON>
from tavily import TavilyClient
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from pydantic import BaseModel
import re
from urllib.parse import urljoin, urlparse
from dotenv import load_dotenv

from collector_models import NewsCollectorConfig, NewsArticle, WebSearchSource, SpecificSource


class ArticleURL(BaseModel):
    """Model for extracted article URLs"""
    urls: List[str]


class NewsCollector:
    """Main news collector class"""
    
    def __init__(self, config_path: str = "config.yaml"):
        # Load environment variables from project root
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        env_path = os.path.join(project_root, '.env')
        load_dotenv(env_path)
        
        self.config_path = config_path
        self.config = self._load_config()
        self.tavily_client = self._init_tavily_client()
        self.url_extractor_agent = self._init_url_extractor_agent()
    
    def _load_config(self) -> NewsCollectorConfig:
        """Load configuration from YAML file"""
        with open(self.config_path, 'r') as file:
            config_dict = yaml.safe_load(file)
        return NewsCollectorConfig(**config_dict)
    
    def _init_tavily_client(self) -> TavilyClient:
        """Initialize Tavily client"""
        api_key = os.getenv("TAVILY_API_KEY")
        if not api_key:
            raise ValueError("TAVILY_API_KEY environment variable is required")
        return TavilyClient(api_key)
    
    def _init_url_extractor_agent(self) -> Agent:
        """Initialize Pydantic AI agent for URL extraction"""
        openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        if not openrouter_api_key:
            raise ValueError("OPENROUTER_API_KEY environment variable is required")
        
        model = OpenAIChatModel(
            "openai/gpt-4o-mini",
            provider=OpenRouterProvider(api_key=openrouter_api_key),
        )
        
        system_prompt = """You are an expert at extracting article URLs from web page content.
        
        Your task is to:
        1. Identify links that point to news articles (not navigation, ads, or other pages)
        2. Filter for articles that appear to be recent based on the time_range provided
        3. Return only the URLs that look like individual news articles
        
        Look for patterns like:
        - URLs containing dates or article IDs
        - Links with article-like titles
        - Content that appears to be news articles rather than category pages
        
        Return only the URLs in the specified format."""
        
        return Agent(
            model,
            output_type=ArticleURL,
            system_prompt=system_prompt,
        )
    
    def collect_all_news(self) -> List[NewsArticle]:
        """Collect news from all configured sources"""
        articles = []
        
        # Collect from web search sources
        for source in self.config.web_search_sources:
            try:
                search_articles = self._collect_from_web_search(source)
                articles.extend(search_articles)
                print(f"Collected {len(search_articles)} articles from web search: {source.name}")
            except Exception as e:
                print(f"Error collecting from web search {source.name}: {e}")
        
        # Collect from specific sources
        for source in self.config.specific_sources:
            try:
                specific_articles = self._collect_from_specific_source(source)
                articles.extend(specific_articles)
                print(f"Collected {len(specific_articles)} articles from specific source: {source.name}")
            except Exception as e:
                print(f"Error collecting from specific source {source.name}: {e}")
        
        return articles
    
    def _collect_from_web_search(self, source: WebSearchSource) -> List[NewsArticle]:
        """Collect articles from web search"""
        try:
            # Search for news using Tavily
            search_response = self.tavily_client.search(
                query=source.query,
                topic="news",
                search_depth="basic",
                time_range=source.time_range,
                max_results=source.max_results,
                include_domains=source.include_domains or [],
                exclude_domains=source.exclude_domains or []
            )
            
            articles = []
            
            # Extract content from each search result
            for result in search_response.get('results', []):
                url = result.get('url')
                title = result.get('title', 'Unknown Title')
                content = result.get('content', '')
                
                if url:
                    # Extract full content from the URL
                    try:
                        extract_response = self.tavily_client.extract(urls=[url])
                        if extract_response and extract_response.get('results'):
                            full_content = extract_response['results'][0].get('content', content)
                        else:
                            full_content = content
                    except:
                        full_content = content
                    
                    article = NewsArticle(
                        title=title,
                        url=url,
                        content=full_content,
                        source_name=source.name
                    )
                    articles.append(article)
            
            return articles
            
        except Exception as e:
            print(f"Error in web search for {source.name}: {e}")
            return []
    
    def _collect_from_specific_source(self, source: SpecificSource) -> List[NewsArticle]:
        """Collect articles from a specific source URL"""
        try:
            # Extract content from the main page
            extract_response = self.tavily_client.extract(urls=[source.url])
            
            if not extract_response or not extract_response.get('results'):
                return []
            
            page_content = extract_response['results'][0].get('content', '')
            
            # Use Pydantic AI agent to extract article URLs
            prompt = f"""
            Extract article URLs from this web page content that appear to be news articles from the last {source.time_range}.
            
            Base URL: {source.url}
            
            Page content:
            {page_content[:8000]}  # Limit content to avoid token limits
            
            Look for links that:
            1. Point to individual news articles
            2. Appear to be recent (within the specified time range)
            3. Are not navigation links, category pages, or advertisements
            
            Return the complete URLs. If URLs are relative, they should be resolved against the base URL.
            """
            
            try:
                result = self.url_extractor_agent.run_sync(prompt)
                article_urls = result.output.urls
            except Exception as e:
                print(f"Error extracting URLs with AI agent: {e}")
                # Fallback to simple regex extraction
                article_urls = self._extract_urls_fallback(page_content, source.url)
            
            # Extract content from each article URL
            articles = []
            for url in article_urls[:10]:  # Limit to 10 articles per source
                try:
                    # Resolve relative URLs
                    full_url = urljoin(source.url, url) if not url.startswith('http') else url
                    
                    # Extract article content
                    article_extract = self.tavily_client.extract(urls=[full_url])
                    
                    if article_extract and article_extract.get('results'):
                        content = article_extract['results'][0].get('content', '')
                        title = self._extract_title_from_content(content) or f"Article from {source.name}"
                        
                        article = NewsArticle(
                            title=title,
                            url=full_url,
                            content=content,
                            source_name=source.name
                        )
                        articles.append(article)
                
                except Exception as e:
                    print(f"Error extracting article from {url}: {e}")
                    continue
            
            return articles
            
        except Exception as e:
            print(f"Error collecting from specific source {source.name}: {e}")
            return []
    
    def _extract_urls_fallback(self, content: str, base_url: str) -> List[str]:
        """Fallback method to extract URLs using regex"""
        # Simple regex to find URLs that look like articles
        url_patterns = [
            r'href=["\']([^"\']+/\d{4}/\d{2}/\d{2}/[^"\']+)["\']',  # Date-based URLs
            r'href=["\']([^"\']+/article/[^"\']+)["\']',             # Article paths
            r'href=["\']([^"\']+/news/[^"\']+)["\']',                # News paths
        ]
        
        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if not match.startswith('http'):
                    match = urljoin(base_url, match)
                urls.append(match)
        
        # Remove duplicates and return first 10
        return list(dict.fromkeys(urls))[:10]
    
    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from article content"""
        lines = content.split('\n')
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line and len(line) > 10 and len(line) < 200:
                # Skip common non-title patterns
                if not any(skip in line.lower() for skip in ['subscribe', 'menu', 'search', 'login', 'advertisement']):
                    return line
        return None


def main():
    """Main function to test the news collector"""
    import sys
    import os
    
    # Add current directory to path for imports
    current_dir = os.path.dirname(__file__)
    if current_dir not in sys.path:
        sys.path.append(current_dir)
    
    # Initialize collector
    collector = NewsCollector(os.path.join(current_dir, "config.yaml"))
    
    # Collect news
    print("Starting news collection...")
    articles = collector.collect_all_news()
    
    print(f"\nCollected {len(articles)} articles total:")
    for i, article in enumerate(articles, 1):
        print(f"{i}. {article.title}")
        print(f"   URL: {article.url}")
        print(f"   Source: {article.source_name}")
        print(f"   Content length: {len(article.content)} characters")
        print()


if __name__ == "__main__":
    main()
