#!/usr/bin/env python3
"""
Working integration example - news collector + AI pipeline
This script runs in the AI parser directory to avoid import conflicts
"""
import sys
import os
import json

# Add both directories to path
news_collector_dir = "/home/<USER>/dev/ai/carbon-regulation-news/.playground/news_collector"
ai_parser_dir = "/home/<USER>/dev/ai/carbon-regulation-news/.playground/ai_source_parser"

# Import from AI parser (we're in its directory)
from news_pipeline_refactored import NewsExtractor, ExtractInput

# Import from news collector
sys.path.append(news_collector_dir)
import importlib.util

# Load news collector modules dynamically to avoid conflicts
spec = importlib.util.spec_from_file_location(
    "collector", 
    os.path.join(news_collector_dir, "collector.py")
)
collector_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(collector_module)

def integration_test():
    """Test full integration between news collector and AI pipeline"""
    print("Full Integration Test: News Collector + AI Pipeline")
    print("=" * 60)
    
    # Initialize news collector
    print("1. Initializing news collector...")
    collector = collector_module.NewsCollector(
        os.path.join(news_collector_dir, "config.yaml")
    )
    
    # Limit collection for demo
    collector.config.web_search_sources = collector.config.web_search_sources[:1]
    collector.config.web_search_sources[0].max_results = 2
    collector.config.specific_sources = []
    
    # Collect articles
    print("2. Collecting news articles...")
    articles = collector.collect_all_news()
    print(f"   Collected {len(articles)} articles")
    
    if not articles:
        print("   No articles collected, ending test")
        return
    
    # Initialize AI extractor
    print("3. Initializing AI extractor...")
    extractor = NewsExtractor()
    
    # Process articles through AI pipeline
    print("4. Processing articles through AI pipeline...")
    processed_articles = []
    
    for i, article in enumerate(articles, 1):
        print(f"   Processing article {i}: {article.title[:50]}...")
        
        try:
            # Create ExtractInput
            extract_input = ExtractInput(
                url=article.url,
                outlet=article.source_name,
                raw_content=article.content
            )
            
            # Extract structured data
            news_item = extractor.extract(extract_input)
            
            # Convert to dict for easier handling
            processed_data = {
                "original": {
                    "title": article.title,
                    "url": article.url,
                    "source": article.source_name
                },
                "extracted": {
                    "title": news_item.content.title,
                    "summary": news_item.content.summary,
                    "category": news_item.classification.category.value,
                    "type": news_item.classification.type.value,
                    "jurisdictions": news_item.classification.jurisdictions,
                    "sectors": news_item.classification.sectors,
                    "key_points": news_item.content.key_points
                }
            }
            
            processed_articles.append(processed_data)
            print(f"   ✅ Successfully processed article {i}")
            
        except Exception as e:
            print(f"   ❌ Error processing article {i}: {e}")
    
    # Display results
    print(f"\n5. Results ({len(processed_articles)} processed successfully):")
    print("=" * 60)
    
    for i, data in enumerate(processed_articles, 1):
        print(f"\nArticle {i}:")
        print(f"Original Title: {data['original']['title']}")
        print(f"Extracted Title: {data['extracted']['title']}")
        print(f"Category: {data['extracted']['category']}")
        print(f"Type: {data['extracted']['type']}")
        print(f"Jurisdictions: {', '.join(data['extracted']['jurisdictions'])}")
        print(f"Summary: {data['extracted']['summary']}")
        print(f"Key Points: {len(data['extracted']['key_points'])} points")
    
    # Export results
    output_file = os.path.join(news_collector_dir, "processed_articles.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(processed_articles, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Integration test complete!")
    print(f"✅ Exported {len(processed_articles)} processed articles to {output_file}")
    
    return processed_articles

if __name__ == "__main__":
    # Change to AI parser directory to avoid import issues
    os.chdir(ai_parser_dir)
    integration_test()
