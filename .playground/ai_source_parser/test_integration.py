#!/usr/bin/env python3
"""
Working integration script
Run this from the ai_source_parser directory
"""
import sys
import os
import json

# We're running from ai_source_parser directory
sys.path.append('../news_collector')

# Import AI pipeline (we're in its directory)
from news_pipeline_refactored import NewsExtractor, ExtractInput

# Import collector using importlib to avoid module conflicts
import importlib.util
spec = importlib.util.spec_from_file_location(
    "collector", 
    "../news_collector/collector.py"
)
collector_module = importlib.util.module_from_spec(spec)

# Execute the collector module in isolated namespace
original_modules = sys.modules.copy()
try:
    spec.loader.exec_module(collector_module)
    
    # Quick test
    print("Testing integration...")
    collector = collector_module.NewsCollector("../news_collector/config.yaml")
    
    # Limit for quick test
    collector.config.web_search_sources = collector.config.web_search_sources[:1]
    collector.config.web_search_sources[0].max_results = 1
    collector.config.specific_sources = []
    
    articles = collector.collect_all_news()
    print(f"Collected {len(articles)} articles")
    
    if articles:
        # Process first article
        article = articles[0]
        print(f"Processing: {article.title[:50]}...")
        
        extractor = NewsExtractor()
        data = ExtractInput(
            url=article.url,
            outlet=article.source_name,
            raw_content=article.content
        )
        
        news_item = extractor.extract(data)
        
        print("✅ INTEGRATION SUCCESSFUL!")
        print(f"Original title: {article.title}")
        print(f"Extracted title: {news_item.content.title}")
        print(f"Category: {news_item.classification.category.value}")
        print(f"Type: {news_item.classification.type.value}")
        print(f"Jurisdictions: {news_item.classification.jurisdictions}")
        print(f"Summary: {news_item.content.summary}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
finally:
    # Restore modules to prevent pollution
    for mod_name in list(sys.modules.keys()):
        if mod_name not in original_modules:
            del sys.modules[mod_name]
